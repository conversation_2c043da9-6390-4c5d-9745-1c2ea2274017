﻿using HIH.Framework.AutoCrawingManager.Result;
using HIH.Framework.Common.Data;
using HIHWCFServiceAPP;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text.RegularExpressions;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IHAPAG : IProcess
    {
        private readonly string SearchMainPage = "https://www.hapag-lloyd.cn/zh/online-business/track/track-by-booking-solution.html?view=S8510&container=";
        public IHAPAG() : base("HAPAG", "") { }
        private string erroMsg = "";


        public override List<IProcessItem> Run(string searchKey, string replaceText)
        {
            try
            {
                erroMsg = "";
                string containerNo = "";
                //获取到当前提单号的所有箱号
                DataTable dt = SqlHelper.FillDataTable("select top 1 c.集装箱箱号 from [uswms].[dbo].[订单表] a," +
                    "[uswms].[dbo].入库通知表 b," +
                    "[uswms].[dbo].库存表 c " +
                    "where a.订单号=b.订单号 and b.单据编号=c.入库通知编号 and a.订单号='" + searchKey + "'");
                if (dt.Rows.Count == 0)
                {
                    erroMsg = "未找到集装箱箱号";
                }
                else
                {
                    containerNo = dt.Rows[0]["集装箱箱号"].ToString();
                }

               

                List<IProcessItem> processList = new List<IProcessItem>();

                IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);

                IProcessItem secPro = new IProcessItem(1, "解析", IProcessType.READ, this.GetResult);



                firPro.JScript = this.SearchMainPage + containerNo;
                secPro.JScript = "document.documentElement.innerHTML";

                processList.Add(firPro);
                processList.Add(secPro);
                return processList;
            }
            catch (Exception exc)
            {
                return new List<IProcessItem>();
            }

        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    if (!string.IsNullOrEmpty(erroMsg))
                    {
                        result.ETAExc = erroMsg;
                    }
                    else
                    {
                        result.ETA = this.GetETA(resultString);
                    }
                    
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }

                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private string GetETA(string result)
        {
            try
            {

                string contentPattern = @"<table[^>]*id=""[^""]*ext-gen46""[^>]*>(.*?)<\/table>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;

                string trPattern = @"<tr[^>]*>(.*?)<\/tr>";

                MatchCollection trMatches = Regex.Matches(matchVal, trPattern, RegexOptions.Singleline);

                if (trMatches.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = "";
                foreach (Match tr in trMatches)
                {

                    string trStr = tr.Value;
                    if (trStr.Contains("Vessel arrived"))
                    {
                        string tdPattern = @"<td[^>]*>(.*?)<\/td>";
                        MatchCollection tdMatches = Regex.Matches(trStr, tdPattern, RegexOptions.Singleline);
                        etaValue = tdMatches.Count > 1 ? tdMatches[2].Groups[1].Value : "";

                        string spanPattern = @"<span[^>]*>(.*?)<\/span>";
                        MatchCollection spanMatches = Regex.Matches(etaValue, spanPattern, RegexOptions.Singleline);
                        etaValue = spanMatches.Count > 0 ? spanMatches[0].Groups[1].Value : "";
                    }
                }
                etaValue = this.ParseExactTime(etaValue);
                if (string.IsNullOrEmpty(etaValue))
                    throw new Exception("未找到正确的匹配对象");

                return etaValue;
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                if (DateTime.TryParse(inputDate, out DateTime result))
                {
                    return result.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new Exception("解析日期失败【" + inputDate + "】");
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }




    }
}
