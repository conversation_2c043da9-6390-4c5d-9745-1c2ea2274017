using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IZIM : IProcess
    {
        private readonly string SearchMainPage = "https://www.zimshipping.com.cn/tools/track-a-shipment?consnumber=";

        public IZIM() : base("ZIM", "") { }

        public override List<IProcessItem> Run(string searchKey, string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage + searchKey;
            secPro.JScript = "document.documentElement.innerHTML";

            processList.Add(firPro);
            processList.Add(secPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception exc)
                {
                    result.ContainerExc = exc.Message;
                }
                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string GetETA(string result)
        {
            try
            {

                result = result.Replace("\\u003C", "<").Replace("\\n", "").Replace("\\", "");

                string contentPattern = @"<div[^>]*class=""col-3 last""[^>]*>([^<]+)</div>";
                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = matches[0].Groups[1].Value;

                if (string.IsNullOrEmpty(etaValue))
                    throw new Exception("ETA为空");

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private string ParseExactTime(string inputDate)
        {
            try
            {
                // 解析原始格式（注意处理区域性差异）
                DateTime date = DateTime.ParseExact(
                    inputDate,
                    "dd-MMM-yyyy",
                    CultureInfo.InvariantCulture // 使用固定区域性确保英文月份解析
                );

                // 转换为目标格式
                return date.ToString("yyyy-MM-dd");

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private BindingList<IResultContainer> GetContainerItems(string containerString)
        {
            try
            {
                BindingList<IResultContainer> containerList = new BindingList<IResultContainer>();

                // 清理HTML字符串
                containerString = containerString.Replace("\\u003C", "<").Replace("\\n", "").Replace("\\", "");

                // 匹配主容器卡片 - 使用更精确的模式来匹配完整的容器区域
                // 先找到所有容器的开始位置
                string containerStartPattern = @"<li[^>]*id=""[^""]*""[^>]*class=""card-item row card-container-v2""[^>]*>";
                MatchCollection containerStartMatches = Regex.Matches(containerString, containerStartPattern);

                List<string> containerHtmlList = new List<string>();

                // 为每个容器开始位置，找到对应的结束位置
                for (int i = 0; i < containerStartMatches.Count; i++)
                {
                    int startPos = containerStartMatches[i].Index;
                    int endPos;

                    if (i < containerStartMatches.Count - 1)
                    {
                        // 不是最后一个容器，结束位置是下一个容器开始之前
                        endPos = containerStartMatches[i + 1].Index;
                    }
                    else
                    {
                        // 最后一个容器，结束位置是字符串末尾
                        endPos = containerString.Length;
                    }

                    // 提取完整的容器HTML
                    string containerHtml = containerString.Substring(startPos, endPos - startPos);
                    containerHtmlList.Add(containerHtml);
                }

                foreach (string containerCardHtml in containerHtmlList)
                {

                    // 提取容器号
                    string containerNoPattern = @"<div[^>]*class=""unit-number""[^>]*>([^<]+)</div>";
                    Match containerNoMatch = Regex.Match(containerCardHtml, containerNoPattern);
                    string containerNo = "";
                    if (containerNoMatch.Success)
                    {
                        containerNo = containerNoMatch.Groups[1].Value.Trim();
                    }

                    if (string.IsNullOrEmpty(containerNo))
                        continue;

                    // 初始化日期变量
                    string returnDateString = "";
                    string pickupDateString = "";
                    string uploadDateString = "";

                    // 直接在当前容器的HTML中搜索所有活动项
                    // 查找所有活动项 - 不需要先提取card-desktop-inner区域
                    string activityItemPattern = @"<li[^>]*class=""card-item row card-container-activity""[^>]*>(.*?)</li>";
                    MatchCollection activityItems = Regex.Matches(containerCardHtml, activityItemPattern, RegexOptions.Singleline);

                    foreach (Match activityItem in activityItems)
                    {
                        string activityItemHtml = activityItem.Value;

                        // 提取日期
                        string datePattern = @"<div[^>]*class=""date""[^>]*>([^<]+)</div>";
                        Match dateMatch = Regex.Match(activityItemHtml, datePattern);
                        string dateValue = "";
                        if (dateMatch.Success)
                        {
                            dateValue = dateMatch.Groups[1].Value.Trim();
                        }

                        // 提取活动描述
                        string activityDescPattern = @"<div[^>]*id=""[^""]*_activityDesc""[^>]*>([^<]+)</div>";
                        Match activityDescMatch = Regex.Match(activityItemHtml, activityDescPattern);
                        string activityDesc = "";
                        if (activityDescMatch.Success)
                        {
                            activityDesc = activityDescMatch.Groups[1].Value.Trim();
                        }

                        // 根据活动描述设置相应的日期
                        if (activityDesc == "Container was discharged at Port of Destination")
                        {
                            uploadDateString = ParseExactTime(dateValue);
                        }
                        else if (activityDesc == "Import truck departure from Port of Discharge to Customer")
                        {
                            pickupDateString = ParseExactTime(dateValue);
                        }
                        else if (activityDesc == "Empty container gate in by Truck")
                        {
                            returnDateString = ParseExactTime(dateValue);
                        }
                    }

                    // 创建容器项目
                    IResultContainer containerItem = new IResultContainer();
                    containerItem.SetNewContainerItem(containerNo, pickupDateString, uploadDateString, returnDateString);
                    containerList.Add(containerItem);
                }

                return containerList;
            }
            catch (Exception)
            {
                throw;
            }
        }

    }
}
